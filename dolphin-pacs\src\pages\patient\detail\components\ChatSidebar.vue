<script lang="ts" setup>
import { ref } from "vue"

defineOptions({
  name: "ChatSidebar"
})

// 聊天相关
const chatMessage = ref('')
const chatMessages = ref()

// 发送聊天消息
const sendMessage = () => {
  if (!chatMessage.value.trim()) return
  
  // 这里可以添加发送消息的逻辑
  console.log('发送消息:', chatMessage.value)
  
  // 清空输入框
  chatMessage.value = ''
}
</script>

<template>
  <!-- 右侧超声聊天区域 -->
  <div class="chat-sidebar">
    <div class="chat-container">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <h3 class="chat-title">海豚AI助手</h3>
      </div>
      
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="chatMessages">
        <div class="message-list">
          <!-- 示例消息 -->
          <div class="message-item user-message">
            <div class="message-content">
              请帮我分析一下这个超声图像
            </div>
            <div class="message-time">14:30</div>
          </div>
          <div class="message-item ai-message">
            <div class="message-content">
              好的，我来为您分析这个超声图像。请上传图像或告诉我具体需要分析的部位。
            </div>
            <div class="message-time">14:31</div>
          </div>
        </div>
      </div>
      
      <!-- 聊天输入区域 -->
      <div class="chat-input">
        <el-input
          v-model="chatMessage"
          type="textarea"
          :rows="3"
          placeholder="输入您的问题..."
          resize="none"
          @keydown.enter.prevent="sendMessage"
        />
        <div class="input-actions">
          <el-button type="primary" @click="sendMessage" :disabled="!chatMessage.trim()">
            发送
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chat-sidebar {
  width: 350px;
  flex-shrink: 0;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chat-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-color-primary);
      border-radius: 8px 8px 0 0;

      .chat-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: white;
      }
    }

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      .message-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .message-item {
          display: flex;
          flex-direction: column;

          &.user-message {
            align-items: flex-end;

            .message-content {
              background: var(--el-color-primary);
              color: white;
              max-width: 80%;
              padding: 10px 14px;
              border-radius: 18px 18px 4px 18px;
              font-size: 14px;
              line-height: 1.4;
            }
          }

          &.ai-message {
            align-items: flex-start;

            .message-content {
              background: var(--el-fill-color-light);
              color: var(--el-text-color-primary);
              max-width: 80%;
              padding: 10px 14px;
              border-radius: 18px 18px 18px 4px;
              font-size: 14px;
              line-height: 1.4;
            }
          }

          .message-time {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
            margin-top: 4px;
            padding: 0 14px;
          }
        }
      }
    }

    .chat-input {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);

      .input-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
      }
    }
  }
}

// 响应式设计 - 在移动端隐藏
@media screen and (max-width: 768px) {
  .chat-sidebar {
    display: none;
  }
}
</style>
