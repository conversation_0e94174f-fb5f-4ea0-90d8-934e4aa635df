<script lang="ts" setup>
defineOptions({
  name: "ImageListItem"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}

interface Props {
  image: ImageData
  isSelected: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  select: []
  delete: []
  toggleVisibility: []
  analyze: []
}>()
</script>

<template>
  <div
    class="list-item"
    :class="{ 'active': isSelected }"
    @click="emit('select')"
  >
    <!-- 左侧图像 -->
    <div class="image-section">
      <div class="image-thumbnail">
        <img :src="image.thumbnail" :alt="image.name" />
      </div>
    </div>

    <!-- 右侧按钮组 -->
    <div class="actions-section">
      <el-button
        size="small"
        :icon="'View'"
        circle
        @click.stop="emit('toggleVisibility')"
        :type="image.fabricObject?.visible !== false ? 'primary' : 'default'"
        title="显示/隐藏"
      />
      <el-button
        size="small"
        :icon="'DataAnalysis'"
        circle
        type="success"
        @click.stop="emit('analyze')"
        title="分析图像"
      />
      <el-button
        size="small"
        :icon="'Delete'"
        circle
        type="danger"
        @click.stop="emit('delete')"
        title="删除图像"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-height: 80px; // 确保有足够的高度

  &:hover {
    background: var(--el-color-primary-light-9);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

    .actions-section {
      opacity: 1;
    }
  }

  &.active {
    background: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  .image-section {
    flex-shrink: 0;

    .image-thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .actions-section {
    display: flex;
    gap: 6px;
    opacity: 0.7;
    transition: opacity 0.2s;
    flex-shrink: 0;
    margin-left: auto; // 确保按钮在右边

    :deep(.el-button) {
      padding: 6px;
      width: 28px;
      height: 28px;
      font-size: 14px;
    }
  }
}
</style>
